/**
 * Team entity model
 * Represents a team in the system with balance tracking and ownership
 */
export class Team {
  /**
   * @param {Object} data - Team data
   * @param {string} data.internal_id - Internal UUID
   * @param {string} data.id - Public team ID
   * @param {Date} data.created_at - Creation timestamp
   * @param {Date} data.next_charge_at - Next charge timestamp
   * @param {number} data.charge_amount - Amount to charge per billing cycle
   * @param {number} data.balance - Team balance
   * @param {string} data.owner_internal_id - Owner's internal ID
   * @param {string} data.owner_id - Owner's public ID
   */
  constructor(data = {}) {
    this.internal_id = data.internal_id;
    this.id = data.id;
    this.created_at = data.created_at ? new Date(data.created_at) : null;
    this.next_charge_at = data.next_charge_at ? new Date(data.next_charge_at) : null;
    this.charge_amount = Number(data.charge_amount) || 0;
    this.balance = Number(data.balance) || 0;
    this.owner_internal_id = data.owner_internal_id;
    this.owner_id = data.owner_id;
  }

  /**
   * Validate team data
   * @param {Partial<Team>} data - Team data to validate
   * @param {string} operation - Operation type (create, update)
   * @returns {{valid: boolean, errors: string[]}}
   */
  static validate(data, operation = 'create') {
    const errors = [];

    if (operation === 'create') {
      if (!data.id || typeof data.id !== 'string') {
        errors.push('Team ID is required and must be a string');
      }
      
      if (data.balance !== undefined && (typeof data.balance !== 'number' || data.balance < 0)) {
        errors.push('Balance must be a non-negative number');
      }
      
      if (data.charge_amount !== undefined && (typeof data.charge_amount !== 'number' || data.charge_amount < 0)) {
        errors.push('Charge amount must be a non-negative number');
      }
    }

    if (operation === 'update') {
      if (data.balance !== undefined && (typeof data.balance !== 'number' || data.balance < 0)) {
        errors.push('Balance must be a non-negative number');
      }
      
      if (data.charge_amount !== undefined && (typeof data.charge_amount !== 'number' || data.charge_amount < 0)) {
        errors.push('Charge amount must be a non-negative number');
      }
    }

    if (data.id && (typeof data.id !== 'string' || data.id.trim().length === 0)) {
      errors.push('Team ID must be a non-empty string');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Convert to database format
   * @returns {Object}
   */
  toDb() {
    return {
      internal_id: this.internal_id,
      id: this.id,
      created_at: this.created_at?.toISOString(),
      next_charge_at: this.next_charge_at?.toISOString(),
      charge_amount: this.charge_amount,
      balance: this.balance,
      owner_internal_id: this.owner_internal_id,
      owner_id: this.owner_id
    };
  }

  /**
   * Create from database data
   * @param {Object} dbData - Raw database data
   * @returns {Team}
   */
  static fromDb(dbData) {
    return new Team(dbData);
  }

  /**
   * Get team summary for API responses
   * @returns {Object}
   */
  toSummary() {
    return {
      id: this.id,
      charge_amount: this.charge_amount,
      balance: this.balance,
      next_charge_at: this.next_charge_at?.toISOString(),
      created_at: this.created_at?.toISOString(),
      owner_id: this.owner_id
    };
  }

  /**
   * Check if team has sufficient balance
   * @param {number} amount - Amount to check
   * @returns {boolean}
   */
  hasSufficientBalance(amount) {
    return this.balance >= amount;
  }

  /**
   * Calculate new balance after transaction
   * @param {number} amount - Transaction amount (positive for credit, negative for debit)
   * @returns {number}
   */
  calculateNewBalance(amount) {
    return this.balance + amount;
  }

  /**
   * Check if team has sufficient balance for the charge amount
   * @returns {boolean}
   */
  hasSufficientBalanceForCharge() {
    return this.balance >= this.charge_amount;
  }

  /**
   * Calculate new balance after applying the charge amount
   * @returns {number}
   */
  calculateBalanceAfterCharge() {
    return this.balance - this.charge_amount;
  }
}

/**
 * Team creation data interface
 */
export class CreateTeamData {
  /**
   * @param {Object} data
   * @param {string} data.id - Public team ID
   * @param {number} data.charge_amount - Amount to charge per billing cycle
   * @param {number} data.balance - Initial balance (default: 0)
   * @param {string} data.owner_internal_id - Owner's internal ID
   * @param {string} data.owner_id - Owner's public ID
   * @param {Date} data.next_charge_at - Next charge timestamp
   */
  constructor(data) {
    this.id = data.id;
    this.charge_amount = data.charge_amount || 0;
    this.balance = data.balance || 0;
    this.owner_internal_id = data.owner_internal_id;
    this.owner_id = data.owner_id;
    this.next_charge_at = data.next_charge_at;
    this.created_at = new Date();
  }
}

/**
 * Team update data interface
 */
export class UpdateTeamData {
  /**
   * @param {Object} data
   * @param {number} data.charge_amount - Amount to charge per billing cycle
   * @param {number} data.balance - New balance
   * @param {Date} data.next_charge_at - Next charge timestamp
   * @param {string} data.owner_internal_id - Owner's internal ID
   * @param {string} data.owner_id - Owner's public ID
   */
  constructor(data) {
    if (data.charge_amount !== undefined) this.charge_amount = data.charge_amount;
    if (data.balance !== undefined) this.balance = data.balance;
    if (data.next_charge_at !== undefined) this.next_charge_at = data.next_charge_at;
    if (data.owner_internal_id !== undefined) this.owner_internal_id = data.owner_internal_id;
    if (data.owner_id !== undefined) this.owner_id = data.owner_id;
  }
}

/**
 * Team statistics interface
 */
export class TeamStatistics {
  /**
   * @param {Object} data
   * @param {number} data.totalTeams - Total number of teams
   * @param {number} data.totalBalance - Sum of all team balances
   * @param {number} data.teamsWithBalance - Teams with positive balance
   * @param {number} data.lowBalanceTeams - Teams with balance below threshold
   * @param {Team[]} data.topTeamsByBalance - Top teams by balance
   * @param {number} data.recentTeams - Recently created teams count
   */
  constructor(data) {
    this.totalTeams = data.totalTeams || 0;
    this.totalBalance = data.totalBalance || 0;
    this.teamsWithBalance = data.teamsWithBalance || 0;
    this.lowBalanceTeams = data.lowBalanceTeams || 0;
    this.topTeamsByBalance = data.topTeamsByBalance || [];
    this.recentTeams = data.recentTeams || 0;
  }
}
