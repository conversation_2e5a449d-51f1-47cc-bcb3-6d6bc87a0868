import { getTeamRepository, getDatabaseFactory } from './database/DatabaseFactory.js';

/**
 * Delete a team and all its associated data
 * @param {string} teamId - The team ID to delete
 * @returns {Promise<{success: boolean, error?: string}>}
 */
export async function deleteTeamWithAssociatedData(teamId) {
  try {
    const teamRepository = getTeamRepository();

    // Check if team exists
    const teamResult = await teamRepository.findByTeamId(teamId);
    if (!teamResult.success || !teamResult.data) {
      return { success: false, error: 'Team not found' };
    }

    // Use the repository's built-in method for deleting team with associated data
    const deleteResult = await teamRepository.deleteTeamWithAssociatedData(teamId);

    if (!deleteResult.success) {
      console.error(`Error deleting team ${teamId}:`, deleteResult.error);
      return {
        success: false,
        error: deleteResult.error?.message || 'Failed to delete team'
      };
    }

    return { success: true };
  } catch (error) {
    console.error(
      `Unexpected error during team deletion for ${teamId}:`,
      error
    );
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, error: `Unexpected error: ${errorMessage}` };
  }
}

/**
 * Get team statistics (count of associated records)
 * @param {string} teamId - The team ID
 * @returns {Promise<{wallets: number, transactions: number, devices: number}>}
 */
export async function getTeamStatistics(teamId) {
  try {
    // Get repositories
    const teamRepository = getTeamRepository();
    // TODO: Uncomment when other repositories are implemented
    // const walletRepository = getWalletRepository();
    // const transactionRepository = getTransactionRepository();
    // const deviceRepository = getDeviceRepository();

    // Check if team exists
    const teamResult = await teamRepository.findByTeamId(teamId);
    if (!teamResult.success || !teamResult.data) {
      console.warn(`Team ${teamId} not found for statistics`);
      return {
        wallets: 0,
        transactions: 0,
        devices: 0,
      };
    }

    // For now, return basic statistics
    // TODO: Implement proper statistics gathering when all repositories are ready
    return {
      wallets: 0, // await walletRepository.count(new QueryOptions({ where: { team_id: teamId } })),
      transactions: 0, // await transactionRepository.count(new QueryOptions({ where: { team_id: teamId } })),
      devices: 0, // await deviceRepository.count(new QueryOptions({ where: { team_id: teamId } })),
    };
  } catch (error) {
    console.error(`Error getting team statistics for ${teamId}:`, error);
    return {
      wallets: 0,
      transactions: 0,
      devices: 0,
    };
  }
}

/**
 * Create a new team
 * @param {Object} teamData - Team creation data
 * @param {string} teamData.id - Team public ID
 * @param {number} teamData.balance - Initial balance
 * @param {string} teamData.owner_id - Owner's public ID
 * @returns {Promise<{success: boolean, data?: any, error?: string}>}
 */
export async function createTeam(teamData) {
  try {
    const teamRepository = getTeamRepository();

    // Check if team already exists
    const existingTeam = await teamRepository.findByTeamId(teamData.id);
    if (existingTeam.success && existingTeam.data) {
      return { success: false, error: 'Team already exists' };
    }

    // Create the team
    const createResult = await teamRepository.create({
      id: teamData.id,
      balance: teamData.balance || 0,
      owner_id: teamData.owner_id,
      created_at: new Date().toISOString()
    });

    if (!createResult.success) {
      return {
        success: false,
        error: createResult.error?.message || 'Failed to create team'
      };
    }

    return { success: true, data: createResult.data };
  } catch (error) {
    console.error(`Error creating team ${teamData.id}:`, error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, error: `Unexpected error: ${errorMessage}` };
  }
}

/**
 * Update team balance
 * @param {string} teamId - Team's public ID
 * @param {number} newBalance - New balance amount
 * @returns {Promise<{success: boolean, data?: any, error?: string}>}
 */
export async function updateTeamBalance(teamId, newBalance) {
  try {
    const teamRepository = getTeamRepository();

    const updateResult = await teamRepository.updateBalance(teamId, newBalance);

    if (!updateResult.success) {
      return {
        success: false,
        error: updateResult.error?.message || 'Failed to update team balance'
      };
    }

    return { success: true, data: updateResult.data };
  } catch (error) {
    console.error(`Error updating balance for team ${teamId}:`, error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, error: `Unexpected error: ${errorMessage}` };
  }
}
