import { env } from '$env/dynamic/private';
import { WestWalletClient } from '$lib/server/WestWalletClient.js';
import { getWalletRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';

// WestWallet API configuration
const WESTWALLET_API_KEY = env.WESTWALLET_API_KEY;
const WESTWALLET_SECRET_KEY = env.WESTWALLET_SECRET_KEY;
const WESTWALLET_BASE_URL =
  env.WESTWALLET_BASE_URL || 'https://api.westwallet.io';

// Supported currencies for wallet generation
const SUPPORTED_CURRENCIES = [
  'USDTTRC',
  'TRX',
  'TON',
  'SOL',
  'NOT',
  'XMR',
  'XRP',
  'DOGE',
];

/**
 * Create WestWallet API client instance
 * @returns {any} WestWallet API client
 */
function createWestWalletClient() {
  if (!WESTWALLET_API_KEY || !WESTWALLET_SECRET_KEY) {
    throw new Error(
      'WestWallet API credentials not configured. Please set WESTWALLET_API_KEY and WESTWALLET_SECRET_KEY environment variables.'
    );
  }

  // Log credential status (without exposing actual values)
  console.log('WestWallet API configuration:', {
    hasApiKey: !!WESTWALLET_API_KEY,
    apiKeyLength: WESTWALLET_API_KEY?.length || 0,
    hasSecretKey: !!WESTWALLET_SECRET_KEY,
    secretKeyLength: WESTWALLET_SECRET_KEY?.length || 0,
    baseUrl: WESTWALLET_BASE_URL,
  });

  return new WestWalletClient(
    WESTWALLET_API_KEY,
    WESTWALLET_SECRET_KEY,
    WESTWALLET_BASE_URL
  );
}

/**
 * Test WestWallet API connection and credentials
 * @returns {Promise<boolean>} True if connection is successful
 */
export async function testWestWalletConnection() {
  try {
    const client = createWestWalletClient();
    console.log('Testing WestWallet API connection...');

    // Try to get wallet balances as a test
    const balances = await client.walletBalances();
    console.log('WestWallet API connection successful:', balances);
    return true;
  } catch (error) {
    console.error('WestWallet API connection failed:', error);
    return false;
  }
}

/**
 * Generate a wallet address for a specific currency
 * @param {string} currency - The currency to generate address for
 * @param {string} teamId - The team ID for labeling
 * @param {string} [ipnUrl] - Optional IPN URL for payment notifications
 
 */
async function generateWalletAddress(currency, teamId, ipnUrl = '') {
  const client = createWestWalletClient();

  try {
    const label = `team_${teamId}_${currency}`;

    // Use provided IPN URL or construct default one
    const baseUrl = env.PUBLIC_BASE_URL || env.ORIGIN || 'https://my-phantomos.com';
    const finalIpnUrl = ipnUrl || `${baseUrl}/api/notifications/ipn`;

    console.log(
      `Attempting to generate ${currency} address with label: ${label}, IPN URL: ${finalIpnUrl}`
    );
    const addressData = await client.generateAddress(
      currency,
      finalIpnUrl,
      label
    );
    console.log(`Successfully generated ${currency} address:`, addressData);

    return {
      address: addressData.address,
      dest_tag: addressData.dest_tag || null,
      currency: currency,
    };
  } catch (error) {
    console.error(
      `Detailed error generating ${currency} wallet address for team ${teamId}:`,
      {
        error: error,
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        name: error instanceof Error ? error.name : 'Unknown',
        constructor:
          error instanceof Error ? error.constructor?.name : 'Unknown',
      }
    );

    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(
      `Failed to generate ${currency} wallet address: ${errorMessage}`
    );
  }
}

/**
 * Generate all required wallets for a team
 * @param {string} teamInternalId - The team's internal UUID
 * @param {string} teamId - The team's public ID
 * @returns {Promise<any[]>} Array of generated wallet data
 */
export async function generateTeamWallets(teamInternalId, teamId) {
  const wallets = [];
  const errors = [];

  console.log(`Generating wallets for team ${teamId} (${teamInternalId})`);

  try {
    const walletRepository = getWalletRepository();
    const walletsToCreate = [];

    // Generate all wallet addresses with delay between requests
    for (const currency of SUPPORTED_CURRENCIES) {
      // Add delay between wallet generations to prevent rate limiting
      if (walletsToCreate.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 600)); // 600ms delay
      }
      try {
        console.log(`Generating ${currency} wallet for team ${teamId}`);

        // Generate wallet address using WestWallet API
        const walletData = await generateWalletAddress(currency, teamId);

        // Prepare wallet record for database
        walletsToCreate.push({
          team_internal_id: teamInternalId,
          team_id: teamId,
          currency: currency,
          address: walletData.address,
          created_at: new Date().toISOString(),
        });

        console.log(
          `Successfully generated ${currency} address for team ${teamId}: ${walletData.address}`
        );
      } catch (error) {
        console.error(
          `Error generating ${currency} wallet address for team ${teamId}:`,
          error
        );
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        errors.push(`Failed to generate ${currency} wallet address: ${errorMessage}`);
      }
    }

    // Use repository to create wallets in batch
    if (walletsToCreate.length > 0) {
      const createResult = await walletRepository.createWalletsForTeam(
        teamId,
        teamInternalId,
        walletsToCreate.map(w => ({ currency: w.currency, address: w.address }))
      );

      if (createResult.success) {
        wallets.push(...createResult.data);
        console.log(`Successfully created ${createResult.data.length} wallets for team ${teamId}`);
      } else {
        console.error(`Error saving wallets to database:`, createResult.error);
        errors.push(`Failed to save wallets: ${createResult.error?.message}`);
      }
    }

    if (errors.length > 0) {
      console.warn(`Some wallets failed to generate for team ${teamId}:`, errors);
      // Don't throw error if some wallets were created successfully
      // Just log the errors for debugging
    }

    console.log(`Generated ${wallets.length} wallets for team ${teamId}`);
    return wallets.map(wallet => wallet.toSummary ? wallet.toSummary() : wallet);
  } catch (error) {
    console.error(`Error in generateTeamWallets for team ${teamId}:`, error);
    throw new Error(`Failed to generate team wallets: ${error.message}`);
  }
}

/**
 * Get all wallets for a team
 * @param {string} teamId - The team's public ID
 * @returns {Promise<any[]>} Array of wallet data
 */
export async function getTeamWallets(teamId) {
  try {
    const walletRepository = getWalletRepository();

    const result = await walletRepository.findByTeamId(teamId, new QueryOptions({
      orderBy: { currency: 'asc' }
    }));

    if (!result.success) {
      console.error(`Error fetching wallets for team ${teamId}:`, result.error);
      throw new Error(`Failed to fetch wallets: ${result.error?.message}`);
    }

    return result.data?.map(wallet => wallet.toSummary ? wallet.toSummary() : wallet) || [];
  } catch (error) {
    console.error(`Error in getTeamWallets for team ${teamId}:`, error);
    throw new Error(`Failed to fetch wallets: ${error.message}`);
  }
}

/**
 * Get wallet by currency for a team
 * @param {string} teamId - The team's public ID
 * @param {string} currency - The currency
 * @returns {Promise<any|null>} Wallet data or null if not found
 */
export async function getTeamWalletByCurrency(teamId, currency) {
  try {
    const walletRepository = getWalletRepository();

    const result = await walletRepository.findByTeamIdAndCurrency(teamId, currency);

    if (!result.success) {
      console.error(
        `Error fetching ${currency} wallet for team ${teamId}:`,
        result.error
      );
      throw new Error(`Failed to fetch ${currency} wallet: ${result.error?.message}`);
    }

    return result.data ? (result.data.toSummary ? result.data.toSummary() : result.data) : null;
  } catch (error) {
    console.error(`Error in getTeamWalletByCurrency for team ${teamId}, currency ${currency}:`, error);
    throw new Error(`Failed to fetch ${currency} wallet: ${error.message}`);
  }
}
