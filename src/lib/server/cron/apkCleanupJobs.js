// src/lib/server/cron/apkCleanupJobs.js

/**
 * APK cleanup cron jobs for managing temporary APK files and batch directories
 */

import fs from 'fs';
import path from 'path';

// APK temporary directory path
const APK_TMP_DIR = path.join(process.cwd(), 'tmp_apk_repacker');

/**
 * Clean up old APK batch directories and files
 * Removes all batch directories older than specified age
 * @param {number} maxAgeDays - Maximum age in days (default: 1 day)
 * @returns {Promise<{success: boolean, cleaned: number, errors: string[], stats: object}>}
 */
export async function cleanupApkBatchDirectories(maxAgeDays = 1) {
  const errors = [];
  let cleaned = 0;
  let totalSize = 0;
  let totalFiles = 0;
  
  try {
    console.log(`[APK Cleanup] Starting cleanup of APK batch directories older than ${maxAgeDays} day(s)`);
    
    // Check if APK temp directory exists
    if (!fs.existsSync(APK_TMP_DIR)) {
      console.log(`[APK Cleanup] APK temp directory does not exist: ${APK_TMP_DIR}`);
      return { 
        success: true, 
        cleaned: 0, 
        errors: [], 
        stats: { totalSize: 0, totalFiles: 0, totalDirectories: 0 }
      };
    }
    
    const cutoffTime = Date.now() - (maxAgeDays * 24 * 60 * 60 * 1000);
    const directories = fs.readdirSync(APK_TMP_DIR);
    
    console.log(`[APK Cleanup] Found ${directories.length} directories to check`);
    
    for (const dir of directories) {
      try {
        const dirPath = path.join(APK_TMP_DIR, dir);
        const stats = fs.statSync(dirPath);
        
        if (stats.isDirectory()) {
          // Check if directory is older than cutoff time
          if (stats.birthtime.getTime() < cutoffTime || stats.mtime.getTime() < cutoffTime) {
            // Calculate directory size before deletion
            const dirStats = await getDirectoryStats(dirPath);
            totalSize += dirStats.size;
            totalFiles += dirStats.files;
            
            // Remove the entire directory
            fs.rmSync(dirPath, { recursive: true, force: true });
            cleaned++;
            
            console.log(`[APK Cleanup] Removed old batch directory: ${dir} (${formatBytes(dirStats.size)}, ${dirStats.files} files)`);
          }
        }
      } catch (error) {
        const errorMsg = `Error cleaning directory ${dir}: ${error.message}`;
        console.error(`[APK Cleanup] ${errorMsg}`);
        errors.push(errorMsg);
      }
    }
    
    console.log(`[APK Cleanup] Cleanup completed. Removed: ${cleaned} directories, ${formatBytes(totalSize)}, ${totalFiles} files`);
    
    return { 
      success: true, 
      cleaned, 
      errors,
      stats: {
        totalSize,
        totalFiles,
        totalDirectories: cleaned,
        totalSizeFormatted: formatBytes(totalSize)
      }
    };
  } catch (error) {
    const errorMsg = `Error in APK cleanup: ${error.message}`;
    console.error(`[APK Cleanup] ${errorMsg}`);
    return { 
      success: false, 
      cleaned, 
      errors: [...errors, errorMsg],
      stats: { totalSize, totalFiles, totalDirectories: cleaned }
    };
  }
}

/**
 * Get statistics about APK temporary directory usage
 * @returns {Promise<{totalSize: number, fileCount: number, directoryCount: number, oldestDirectory: string, newestDirectory: string}>}
 */
export async function getApkDirectoryStats() {
  try {
    if (!fs.existsSync(APK_TMP_DIR)) {
      return { 
        totalSize: 0, 
        fileCount: 0, 
        directoryCount: 0,
        oldestDirectory: null,
        newestDirectory: null,
        totalSizeFormatted: '0 Bytes'
      };
    }
    
    let totalSize = 0;
    let fileCount = 0;
    let directoryCount = 0;
    let oldestTime = Date.now();
    let newestTime = 0;
    let oldestDirectory = null;
    let newestDirectory = null;
    
    const directories = fs.readdirSync(APK_TMP_DIR);
    
    for (const dir of directories) {
      try {
        const dirPath = path.join(APK_TMP_DIR, dir);
        const stats = fs.statSync(dirPath);
        
        if (stats.isDirectory()) {
          directoryCount++;
          
          // Track oldest and newest directories
          const dirTime = stats.birthtime.getTime();
          if (dirTime < oldestTime) {
            oldestTime = dirTime;
            oldestDirectory = dir;
          }
          if (dirTime > newestTime) {
            newestTime = dirTime;
            newestDirectory = dir;
          }
          
          // Get directory size and file count
          const dirStats = await getDirectoryStats(dirPath);
          totalSize += dirStats.size;
          fileCount += dirStats.files;
        }
      } catch (error) {
        console.error(`[APK Stats] Error reading directory ${dir}:`, error);
      }
    }
    
    return { 
      totalSize, 
      fileCount, 
      directoryCount,
      oldestDirectory,
      newestDirectory,
      totalSizeFormatted: formatBytes(totalSize)
    };
  } catch (error) {
    console.error('[APK Stats] Error getting directory stats:', error);
    return { 
      totalSize: 0, 
      fileCount: 0, 
      directoryCount: 0,
      oldestDirectory: null,
      newestDirectory: null,
      totalSizeFormatted: '0 Bytes'
    };
  }
}

/**
 * Force cleanup of all APK directories except very recent ones (last 10 minutes)
 * Use with caution - this is aggressive cleanup
 * @returns {Promise<{success: boolean, cleaned: number, errors: string[]}>}
 */
export async function forceCleanupAllApkDirectories() {
  console.log('[APK Cleanup] Starting FORCE cleanup - removing all directories except last 10 minutes');
  
  // Only keep directories from the last 10 minutes
  const maxAgeMinutes = 10;
  const maxAgeMs = maxAgeMinutes * 60 * 1000;
  
  return await cleanupApkBatchDirectories(maxAgeMs / (24 * 60 * 60 * 1000)); // Convert to days
}

/**
 * Get size and file count of a directory recursively
 * @param {string} dirPath - Path to directory
 * @returns {Promise<{size: number, files: number}>}
 */
async function getDirectoryStats(dirPath) {
  let totalSize = 0;
  let fileCount = 0;
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      try {
        const itemPath = path.join(dirPath, item);
        const stats = fs.statSync(itemPath);
        
        if (stats.isFile()) {
          totalSize += stats.size;
          fileCount++;
        } else if (stats.isDirectory()) {
          const subStats = await getDirectoryStats(itemPath);
          totalSize += subStats.size;
          fileCount += subStats.files;
        }
      } catch (error) {
        console.error(`[APK Stats] Error reading item ${item}:`, error);
      }
    }
  } catch (error) {
    console.error(`[APK Stats] Error reading directory ${dirPath}:`, error);
  }
  
  return { size: totalSize, files: fileCount };
}

/**
 * Format bytes to human readable format
 * @param {number} bytes 
 * @returns {string}
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get cleanup recommendations based on current disk usage
 * @returns {Promise<{shouldCleanup: boolean, reason: string, stats: object}>}
 */
export async function getApkCleanupRecommendations() {
  const stats = await getApkDirectoryStats();
  const sizeInMB = stats.totalSize / (1024 * 1024);
  
  let shouldCleanup = false;
  let reason = 'No cleanup needed';
  
  if (sizeInMB > 1000) { // More than 1GB
    shouldCleanup = true;
    reason = `High disk usage: ${stats.totalSizeFormatted}`;
  } else if (stats.directoryCount > 50) { // More than 50 batch directories
    shouldCleanup = true;
    reason = `Too many batch directories: ${stats.directoryCount}`;
  } else if (stats.fileCount > 200) { // More than 200 files
    shouldCleanup = true;
    reason = `Too many files: ${stats.fileCount}`;
  }
  
  return {
    shouldCleanup,
    reason,
    stats
  };
}
