<script>
  /** @type {import('./$types').PageData} */
  export let data;

  // Helper function to format numbers
  function formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  // Helper function to format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  }

  // Helper function to format percentage
  function formatPercentage(num) {
    return `${num.toFixed(1)}%`;
  }

  // Helper function to get trend indicator
  function getTrendIndicator(current, previous) {
    if (previous === 0) return { icon: '→', color: '#666', text: 'No data' };
    const change = ((current - previous) / previous) * 100;
    if (change > 0) return { icon: '↗', color: '#4caf50', text: `+${change.toFixed(1)}%` };
    if (change < 0) return { icon: '↘', color: '#f44336', text: `${change.toFixed(1)}%` };
    return { icon: '→', color: '#666', text: '0%' };
  }

  // Helper function to get health status color
  function getHealthColor(percentage) {
    if (percentage >= 80) return '#4caf50';
    if (percentage >= 60) return '#ff9800';
    return '#f44336';
  }

  $: stats = data.stats || {};
  $: hasError = data.error;
  $: teamGrowthTrend = getTrendIndicator(stats.newTeamsThisMonth, stats.newTeamsLastMonth);
</script>

<svelte:head>
  <title>Phantom - Admin Panel | Overview</title>
</svelte:head>

{#if hasError}
  <div class="admin-card error-card">
    <div class="admin-card-title">Error Loading Data</div>
    <p>Unable to load overview statistics. Please check the database connection.</p>
  </div>
{/if}

<!-- Executive Summary -->
<div class="admin-card">
  <div class="admin-card-title">
    📊 Executive Dashboard
  </div>

  <!-- Key Performance Indicators -->
  <div class="kpi-grid">
    <div class="kpi-card revenue">
      <div class="kpi-header">
        <span class="kpi-icon">💰</span>
        <span class="kpi-title">Total Revenue</span>
      </div>
      <div class="kpi-value">{formatCurrency(stats.totalRevenue)}</div>
      <div class="kpi-subtitle">All-time earnings</div>
    </div>

    <div class="kpi-card customers">
      <div class="kpi-header">
        <span class="kpi-icon">👥</span>
        <span class="kpi-title">Active Teams</span>
      </div>
      <div class="kpi-value">{formatNumber(stats.totalTeams)}</div>
      <div class="kpi-subtitle">
        <span class="trend-indicator" style="color: {teamGrowthTrend.color}">
          {teamGrowthTrend.icon} {teamGrowthTrend.text}
        </span>
      </div>
    </div>

    <div class="kpi-card balance">
      <div class="kpi-header">
        <span class="kpi-icon">💳</span>
        <span class="kpi-title">Total Balance</span>
      </div>
      <div class="kpi-value">{formatCurrency(stats.totalBalance)}</div>
      <div class="kpi-subtitle">Customer credits</div>
    </div>

    <div class="kpi-card devices">
      <div class="kpi-header">
        <span class="kpi-icon">📱</span>
        <span class="kpi-title">Connected Devices</span>
      </div>
      <div class="kpi-value">{formatNumber(stats.totalDevices)}</div>
      <div class="kpi-subtitle">{formatNumber(stats.activeDevices)} active (7d)</div>
    </div>
  </div>
</div>

<!-- Revenue Analytics -->
<div class="admin-card">
  <div class="admin-card-title">
    📈 Revenue Analytics
  </div>

  <div class="revenue-grid">
    <div class="revenue-card">
      <div class="revenue-label">Monthly Revenue</div>
      <div class="revenue-value">{formatCurrency(stats.monthlyRevenue)}</div>
      <div class="revenue-description">Last 30 days</div>
    </div>

    <div class="revenue-card">
      <div class="revenue-label">Weekly Revenue</div>
      <div class="revenue-value">{formatCurrency(stats.weeklyRevenue)}</div>
      <div class="revenue-description">Last 7 days</div>
    </div>

    <div class="revenue-card">
      <div class="revenue-label">Avg Revenue/Team</div>
      <div class="revenue-value">{formatCurrency(stats.avgRevenuePerTeam)}</div>
      <div class="revenue-description">Per customer</div>
    </div>

    <div class="revenue-card">
      <div class="revenue-label">Payment Rate</div>
      <div class="revenue-value">{formatPercentage(stats.activeTeamRate)}</div>
      <div class="revenue-description">Teams with balance</div>
    </div>
  </div>
</div>

<!-- Operational Metrics -->
<div class="admin-card">
  <div class="admin-card-title">
    ⚙️ Operational Metrics
  </div>

  <div class="operational-grid">
    <div class="operational-card">
      <div class="operational-header">
        <span class="operational-icon">🔧</span>
        <span class="operational-title">Device Utilization</span>
      </div>
      <div class="operational-value" style="color: {getHealthColor(stats.deviceUtilizationRate)}">
        {formatPercentage(stats.deviceUtilizationRate)}
      </div>
      <div class="operational-description">Active devices (7d)</div>
      <div class="progress-bar">
        <div class="progress-fill" style="width: {stats.deviceUtilizationRate}%; background-color: {getHealthColor(stats.deviceUtilizationRate)}"></div>
      </div>
    </div>

    <div class="operational-card">
      <div class="operational-header">
        <span class="operational-icon">📊</span>
        <span class="operational-title">Avg Devices/Team</span>
      </div>
      <div class="operational-value">{stats.avgDevicesPerTeam.toFixed(1)}</div>
      <div class="operational-description">Device density</div>
    </div>

    <div class="operational-card">
      <div class="operational-header">
        <span class="operational-icon">⚠️</span>
        <span class="operational-title">Low Balance Teams</span>
      </div>
      <div class="operational-value" style="color: {stats.lowBalanceTeams > 0 ? '#f44336' : '#4caf50'}">
        {stats.lowBalanceTeams}
      </div>
      <div class="operational-description">Need attention</div>
    </div>

    <div class="operational-card">
      <div class="operational-header">
        <span class="operational-icon">🆕</span>
        <span class="operational-title">New Devices</span>
      </div>
      <div class="operational-value">{stats.newDevicesThisWeek}</div>
      <div class="operational-description">This week</div>
    </div>
  </div>
</div>

<!-- Top Performers & Insights -->
<div class="admin-card">
  <div class="admin-card-title">
    🏆 Top Performers & Insights
  </div>
  <div class="insights-2col-grid">
    <div class="insights-section">
      <h4>Top Teams by Balance</h4>
      <div class="top-teams-list">
        {#each stats.topTeams.slice(0, 5) as team, index}
          <div class="top-team-item">
            <span class="team-rank">#{index + 1}</span>
            <span class="team-id">{team.id}</span>
            <span class="team-balance">{formatCurrency(team.balance)}</span>
          </div>
        {/each}
        {#if stats.topTeams.length === 0}
          <div class="empty-state">No team data available</div>
        {/if}
      </div>
    </div>
    <div class="insights-section">
      <h4>Business Health</h4>
      <div class="health-metrics">
        <div class="health-item">
          <span class="health-label">Teams with Balance:</span>
          <span class="health-value" style="color: {getHealthColor(stats.activeTeamRate)}">
            {stats.teamsWithBalance}/{stats.totalTeams} ({formatPercentage(stats.activeTeamRate)})
          </span>
        </div>
        <div class="health-item">
          <span class="health-label">Total Wallets:</span>
          <span class="health-value">{stats.totalWallets}</span>
        </div>
        <div class="health-item">
          <span class="health-label">Total Transactions:</span>
          <span class="health-value">{formatNumber(stats.totalTransactions)}</span>
        </div>
        <div class="health-item">
          <span class="health-label">Avg Balance/Team:</span>
          <span class="health-value">{formatCurrency(stats.avgBalancePerTeam)}</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Charge Amount & Device Language Analytics -->
<div class="admin-card">
  <div class="analytics-2col-grid">
    <div class="insights-section">
      <h4><span class="insight-icon">💸</span> Charge Amount Analytics</h4>
      {#if stats.chargeAmountStats}
        <div class="charge-amount-grid">
          <div class="charge-stat">
            <div class="charge-label">Total</div>
            <div class="charge-value">{formatCurrency(stats.chargeAmountStats.total)}</div>
          </div>
          <div class="charge-stat">
            <div class="charge-label">Average</div>
            <div class="charge-value">{formatCurrency(stats.chargeAmountStats.avg)}</div>
          </div>
          <div class="charge-stat">
            <div class="charge-label">Min</div>
            <div class="charge-value">{formatCurrency(stats.chargeAmountStats.min)}</div>
          </div>
          <div class="charge-stat">
            <div class="charge-label">Max</div>
            <div class="charge-value">{formatCurrency(stats.chargeAmountStats.max)}</div>
          </div>
        </div>
      {:else}
        <div class="empty-state">No charge amount analytics available</div>
      {/if}
    </div>
    <div class="insights-section">
      <h4><span class="insight-icon">🌐</span> Device Language Distribution</h4>
      <div class="lang-table-card">
        {#if stats.deviceLangDistribution && Object.keys(stats.deviceLangDistribution).length > 0}
          <table class="lang-table">
            <thead>
              <tr>
                <th>Language</th>
                <th style="text-align:right;">Device Count</th>
              </tr>
            </thead>
            <tbody>
              {#each Object.entries(stats.deviceLangDistribution) as [lang, count]}
                <tr>
                  <td>{lang}</td>
                  <td style="text-align:right;">{count}</td>
                </tr>
              {/each}
            </tbody>
          </table>
        {:else}
          <div class="empty-state">No device language data available</div>
        {/if}
      </div>
    </div>
  </div>
</div>

<!-- Revenue Trend -->
{#if stats.dailyRevenueTrend && stats.dailyRevenueTrend.length > 0}
<div class="admin-card">
  <div class="admin-card-title">
    📊 7-Day Revenue Trend
  </div>

  <div class="revenue-trend">
    <div class="trend-chart">
      {#each stats.dailyRevenueTrend as day}
        <div class="trend-bar">
          <div class="bar-fill" style="height: {Math.max(5, (day.revenue / Math.max(...stats.dailyRevenueTrend.map(d => d.revenue))) * 100)}%"></div>
          <div class="bar-label">{new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}</div>
          <div class="bar-value">{formatCurrency(day.revenue)}</div>
        </div>
      {/each}
    </div>
  </div>
</div>
{/if}
<style>
  .error-card {
    background-color: #ffebee;
    border-left: 4px solid #f44336;
  }

  .error-card p {
    color: #c62828;
    margin: 0;
  }

  /* KPI Grid */
  .kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .kpi-card {
    background: linear-gradient(135deg, var(--bg-input) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    border: 1px solid var(--border-secondary);
    transition: transform 0.2s ease;
  }

  .kpi-card:hover {
    transform: translateY(-2px);
  }

  .kpi-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .kpi-icon {
    font-size: 1.5rem;
  }

  .kpi-title {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  .kpi-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-brand);
    margin-bottom: 0.5rem;
  }

  .kpi-subtitle {
    font-size: 0.8rem;
    color: var(--text-secondary);
  }

  .trend-indicator {
    font-weight: 600;
  }

  /* Revenue Grid */
  .revenue-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .revenue-card {
    background-color: var(--bg-input);
    border-radius: var(--radius-md);
    padding: 1rem;
    text-align: center;
  }

  .revenue-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
  }

  .revenue-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--success);
    margin-bottom: 0.25rem;
  }

  .revenue-description {
    font-size: 0.75rem;
    color: var(--text-muted);
  }

  /* Operational Grid */
  .operational-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
  }

  .operational-card {
    background-color: var(--bg-input);
    border-radius: var(--radius-md);
    padding: 1rem;
  }

  .operational-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .operational-icon {
    font-size: 1.2rem;
  }

  .operational-title {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
  }

  .operational-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
  }

  .operational-description {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
  }

  .progress-bar {
    width: 100%;
    height: 4px;
    background-color: var(--border-primary);
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    transition: width 0.3s ease;
  }

  /* Insights Grid */
  .insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .insights-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
  }

  .top-teams-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .top-team-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background-color: var(--bg-input);
    border-radius: var(--radius-sm);
  }

  .team-rank {
    font-weight: 700;
    color: var(--primary-brand);
    min-width: 2rem;
  }

  .team-id {
    flex: 1;
    color: var(--text-primary);
    font-family: monospace;
  }

  .team-balance {
    font-weight: 600;
    color: var(--success);
  }

  .health-metrics {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .health-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-primary);
  }

  .health-item:last-child {
    border-bottom: none;
  }

  .health-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
  }

  .health-value {
    font-weight: 600;
    color: var(--text-primary);
  }

  /* Revenue Trend Chart */
  .revenue-trend {
    margin-top: 1rem;
  }

  .trend-chart {
    display: flex;
    align-items: end;
    gap: 0.5rem;
    height: 150px;
    padding: 1rem 0;
  }

  .trend-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
  }

  .bar-fill {
    width: 100%;
    background: linear-gradient(to top, var(--primary-brand), var(--success));
    border-radius: 2px 2px 0 0;
    min-height: 5px;
    margin-bottom: auto;
  }

  .bar-label {
    font-size: 0.7rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
  }

  .bar-value {
    font-size: 0.65rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
  }

  .empty-state {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 1rem;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .kpi-grid {
      grid-template-columns: 1fr;
    }

    .revenue-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .operational-grid {
      grid-template-columns: 1fr;
    }

    .insights-grid {
      grid-template-columns: 1fr;
    }

    .trend-chart {
      height: 120px;
    }

    .kpi-value {
      font-size: 1.5rem;
    }
  }
  .insight-icon {
    margin-right: 0.5rem;
    font-size: 1.1rem;
    vertical-align: middle;
  }
  .charge-amount-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 0.5rem;
  }
  .charge-stat {
    background: var(--bg-input);
    border-radius: var(--radius-sm);
    padding: 0.75rem 1rem;
    text-align: center;
    border: 1px solid var(--border-secondary);
  }
  .charge-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
  }
  .charge-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--success);
  }
  .lang-table-card {
    background: var(--bg-input);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-secondary);
    padding: 1rem;
    margin-top: 0.5rem;
    overflow-x: auto;
  }
  .lang-table {
    width: 100%;
    border-collapse: collapse;
  }
  .lang-table th, .lang-table td {
    padding: 0.5rem;
    font-size: 0.95rem;
    border-bottom: 1px solid var(--border-primary);
  }
  .lang-table th {
    color: var(--text-secondary);
    font-weight: 600;
    background: none;
  }
  .lang-table tr:last-child td {
    border-bottom: none;
  }
  .lang-table td {
    font-family: monospace;
    color: var(--text-primary);
  }
  .charge-amount .team-balance {
    color: var(--info);
  }
  .insights-3col-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 1.5rem;
  }
  .insights-section {
    display: flex;
    flex-direction: column;
    min-width: 0;
  }
  .lang-table-card h5 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    color: var(--text-secondary);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  @media (max-width: 1024px) {
    .insights-3col-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }
  .insights-2col-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 1.5rem;
  }
  .analytics-2col-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
  @media (max-width: 1024px) {
    .insights-2col-grid, .analytics-2col-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }
</style>
