import { getDeviceRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';
import { verifySession, getSession } from '$lib/server/session';
import { verifyCsrfToken } from '$lib/server/csrf';
import { dev } from '$app/environment';
import { fail } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';

export async function load() {
  try {
    const deviceRepository = getDeviceRepository();

    // Get device statistics and all devices with team info
    const [
      deviceStatsResult,
      devicesWithTeamResult
    ] = await Promise.all([
      deviceRepository.getStatistics(),
      deviceRepository.findWithTeamInfo(new QueryOptions({
        orderBy: { created_at: 'desc' }
      }))
    ]);

    if (!deviceStatsResult.success) {
      console.error('Error getting device statistics:', deviceStatsResult.error);
      throw new Error('Failed to load device statistics');
    }

    if (!devicesWithTeamResult.success) {
      console.error('Error fetching devices:', devicesWithTeamResult.error);
      throw new Error('Failed to load device data');
    }

    const deviceStats = deviceStatsResult.data;
    const devices = devicesWithTeamResult.data || [];

    // Get the most recent device
    const recentDevice = devices.length > 0 ? devices[0] : null;

    return {
      devices: devices.map(device => device.toSummary ? device.toSummary() : device),
      totalDevices: deviceStats.totalDevices,
      recentDevice: recentDevice ? (recentDevice.toSummary ? recentDevice.toSummary() : recentDevice) : null,
      error: null,
    };
  } catch (error) {
    console.error('Error in devices page load:', error);
    return {
      devices: [],
      totalDevices: 0,
      recentDevice: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}

export const actions = {
  delete: async ({ request }) => {
    const formData = await request.formData();
    const id = formData.get('id');

    if (!id || typeof id !== 'string') {
      return fail(400, { success: false, error: 'Missing or invalid device ID' });
    }

    try {
      const deviceRepository = getDeviceRepository();
      const deleteResult = await deviceRepository.deleteById(id);

      if (!deleteResult.success) {
        console.error('Error deleting device:', deleteResult.error);
        return fail(500, { success: false, error: deleteResult.error?.message || 'Failed to delete device' });
      }

      return { success: true };
    } catch (error) {
      console.error('Error deleting device:', error);
      return fail(500, { success: false, error: error.message });
    }
  },

  // Generate 3 APKs for a single device using all available templates
  generateDeviceApks: async ({ request, cookies }) => {
    console.log('generateDeviceApks action started');
    const data = await request.formData();
    const deviceId = data.get('deviceId');
    const csrfToken = data.get('csrfToken');

    // Verify the session
    const signedSession = cookies.get('admin_session');
    const sessionId = verifySession(signedSession);
    const session = sessionId ? getSession(sessionId) : null;

    // If not authenticated or CSRF token is invalid, return error
    if (!dev && !session) {
      return fail(401, { success: false, error: 'Authentication required' });
    }

    // Verify CSRF token
    if (!dev && !sessionId) {
      return fail(401, { success: false, error: 'Invalid session' });
    }

    if (csrfToken) {
      const tokenStr = typeof csrfToken === 'string' ? csrfToken : String(csrfToken);
      if (!dev && !verifyCsrfToken(tokenStr, sessionId)) {
        return fail(403, { success: false, error: 'Invalid request token' });
      }
    } else {
      return fail(400, { success: false, error: 'CSRF token required' });
    }

    // Validate device ID
    if (!deviceId || typeof deviceId !== 'string') {
      return fail(400, { success: false, error: 'Missing or invalid device ID' });
    }

    try {
      const deviceRepository = getDeviceRepository();
      const deviceResult = await deviceRepository.findById(deviceId);

      if (!deviceResult.success || !deviceResult.data) {
        return fail(404, { success: false, error: 'Device not found', deviceId });
      }

      const device = deviceResult.data;

      // Check if device has the required configurations
      if (!device.vpn_conf || !device.msg_conf || !device.phone_conf) {
        return fail(400, {
          success: false,
          error: 'Device is missing required configurations (vpn_conf, msg_conf, or phone_conf)',
          deviceId
        });
      }

      // Check if templates exist
      const templatesDir = path.join(process.cwd(), '../apk-templates');
      if (!fs.existsSync(templatesDir)) {
        return fail(500, { success: false, error: 'APK templates directory not found', deviceId });
      }

      const templateFiles = fs.readdirSync(templatesDir).filter(file =>
        file.toLowerCase().endsWith('.apk')
      );

      if (templateFiles.length === 0) {
        return fail(404, { success: false, error: 'No APK templates found', deviceId });
      }

      // Generate a unique batch ID for this device's APKs
      const batchId = `batch-${Date.now()}`;
      const generatedApks = [];
      const errors = [];

      console.log(`Starting APK generation for device ${device.internal_id} using ${templateFiles.length} templates with batch ID: ${batchId}`);

      // Generate APKs using all available templates
      for (let i = 0; i < templateFiles.length; i++) {
        const templateFile = templateFiles[i];

        try {
          const templatePath = path.join(templatesDir, templateFile);
          const templateName = path.basename(templateFile, '.apk');

          // Generate appropriate configuration based on template type using centralized logic
          const templateType = ApkRepackerService.getTemplateType(templateName);
          const templateConfig = ApkRepackerService.getTemplateConfig(templateType);
          const config = ApkRepackerService.generateDeviceConfig(templateType, device);
          const configFilename = templateConfig.filename;

          // Generate a unique job ID for this template
          const jobId = `${batchId}-device-${device.internal_id}-${templateName}-${Date.now() + i}`;

          // Read the template file
          const templateBuffer = fs.readFileSync(templatePath);

          // Create repacking job with appropriate config filename
          const repackResult = await ApkRepackerService.createRepackingJob(
            templateBuffer,
            config,
            configFilename,
            jobId
          );

          if (repackResult.success) {
            generatedApks.push({
              templateName,
              jobId: repackResult.jobId,
              filename: repackResult.apkName || `${templateName}-${device.nickname || device.internal_id}.apk`,
              downloadUrl: `/download/apk/${batchId}/${repackResult.jobId}/${repackResult.apkName}`
            });
            console.log(`Successfully generated APK for device ${device.internal_id} using template ${templateName}`);
          } else {
            const errorMsg = `Failed to generate APK using template ${templateName}: ${repackResult.error}`;
            console.error(errorMsg);
            errors.push(errorMsg);
          }
        } catch (error) {
          const errorMsg = `Error generating APK using template ${templateFile}: ${error.message}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      console.log(`APK generation completed for device ${device.internal_id}. Generated: ${generatedApks.length}, Errors: ${errors.length}`);

      if (generatedApks.length === 0) {
        return fail(500, {
          success: false,
          error: 'Failed to generate any APKs',
          details: errors.join('; '),
          deviceId
        });
      }

      return {
        success: true,
        deviceId: device.internal_id,
        deviceNickname: device.nickname || `Device-${device.internal_id.slice(0, 6)}`,
        batchId,
        generatedApks,
        totalGenerated: generatedApks.length,
        downloadPageUrl: `/download/apk/${batchId}`,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      console.error('Error in generateDeviceApks:', error);
      return fail(500, {
        success: false,
        error: error.message || 'An error occurred while generating APKs',
        deviceId
      });
    }
  },
  
  // Generate APK with device configurations
  generateDeviceApk: async ({ request, cookies }) => {
    console.log('generateDeviceApk')
    const data = await request.formData();
    const deviceId = data.get('deviceId');
    const csrfToken = data.get('csrfToken');
    
    // Verify the session
    const signedSession = cookies.get('admin_session');
    const sessionId = verifySession(signedSession);
    const session = sessionId ? getSession(sessionId) : null;

    // If not authenticated or CSRF token is invalid, return error
    if (!dev && !session) {
      return fail(401, { success: false, error: 'Authentication required' });
    }

    // Verify CSRF token
    if (!dev && !sessionId) {
      return fail(401, { success: false, error: 'Invalid session' });
    }

    if (csrfToken) {
      const tokenStr = typeof csrfToken === 'string' ? csrfToken : String(csrfToken);
      if (!dev && !verifyCsrfToken(tokenStr, sessionId)) {
        return fail(403, { success: false, error: 'Invalid request token' });
      }
    } else {
      return fail(400, { success: false, error: 'CSRF token required' });
    }

    // Validate device ID
    if (!deviceId || typeof deviceId !== 'string') {
      return fail(400, { success: false, error: 'Missing or invalid device ID' });
    }

    try {
      const deviceRepository = getDeviceRepository();
      const deviceResult = await deviceRepository.findById(deviceId);
      
      if (!deviceResult.success || !deviceResult.data) {
        return fail(404, { success: false, error: 'Device not found' });
      }

      const device = deviceResult.data;
      
      // Check if device has the required configurations
      if (!device.vpn_conf || !device.msg_conf || !device.phone_conf) {
        return fail(400, {
          success: false,
          error: 'Device is missing required configurations (vpn_conf or msg_conf or phone_conf)'
        });
      }

      // Use the first available template or specify a default one
      const templatesDir = path.join(process.cwd(), '../apk-templates');
      const templateFiles = fs.readdirSync(templatesDir).filter(file => 
        file.toLowerCase().endsWith('.apk')
      );

      if (templateFiles.length === 0) {
        return fail(404, { success: false, error: 'No APK templates found' });
      }

      const templatePath = path.join(templatesDir, templateFiles[0]);
      const templateName = path.basename(templateFiles[0], '.apk');

      // Generate appropriate configuration based on template type using centralized logic
      const templateType = ApkRepackerService.getTemplateType(templateName);
      const templateConfig = ApkRepackerService.getTemplateConfig(templateType);
      const config = ApkRepackerService.generateDeviceConfig(templateType, device);
      const configFilename = templateConfig.filename;

      // Generate a unique job ID
      const jobId = `device-${device.internal_id}-${Date.now()}`;
      
      // Use ApkRepackerService to generate the APK
      // First, read the template file
      const templateFile = fs.readFileSync(templatePath);
      
      // Create repacking job with appropriate config filename
      const repackResult = await ApkRepackerService.createRepackingJob(
        templateFile,
        config,
        configFilename,
        jobId
      );
      
      if (!repackResult.success) {
        console.error('APK generation failed:', repackResult.error);
        return fail(500, {
          success: false,
          error: repackResult.error || 'Failed to generate APK'
        });
      }

      return { 
        success: true, 
        downloadUrl: repackResult.downloadUrl,
        filename: repackResult.filename || `phantom-${device.nickname || device.internal_id}.apk`
      };
    } catch (error) {
      console.error('Error generating APK for device:', error);
      return fail(500, {
        success: false,
        error: error.message || 'An error occurred while generating the APK'
      });
    }
  },

  // Generate specific APK types for a device based on user selection
  generateSpecificApk: async ({ request, cookies }) => {
    console.log('generateSpecificApk action started');
    const data = await request.formData();
    const deviceId = data.get('deviceId');
    const apkType = data.get('apkType'); // 'all', 'vpn', 'messenger', 'phantom', 'caller', 'billing'
    const csrfToken = data.get('csrfToken');

    // Verify the session
    const signedSession = cookies.get('admin_session');
    const sessionId = verifySession(signedSession);
    const session = sessionId ? getSession(sessionId) : null;

    // If not authenticated or CSRF token is invalid, return error
    if (!dev && !session) {
      return fail(401, { success: false, error: 'Authentication required' });
    }

    // Verify CSRF token
    if (!dev && !sessionId) {
      return fail(401, { success: false, error: 'Invalid session' });
    }

    if (csrfToken) {
      const tokenStr = typeof csrfToken === 'string' ? csrfToken : String(csrfToken);
      if (!dev && !verifyCsrfToken(tokenStr, sessionId)) {
        return fail(403, { success: false, error: 'Invalid request token' });
      }
    } else {
      return fail(400, { success: false, error: 'CSRF token required' });
    }

    // Validate device ID
    if (!deviceId || typeof deviceId !== 'string') {
      return fail(400, { success: false, error: 'Missing or invalid device ID' });
    }

    // Validate APK type
    const validApkTypes = ['all', 'vpn', 'messenger', 'phantom', 'caller'];
    if (!apkType || !validApkTypes.includes(apkType)) {
      return fail(400, { success: false, error: 'Invalid APK type specified' });
    }

    try {
      const deviceRepository = getDeviceRepository();
      const deviceResult = await deviceRepository.findById(deviceId);

      if (!deviceResult.success || !deviceResult.data) {
        return fail(404, { success: false, error: 'Device not found', deviceId });
      }

      const device = deviceResult.data;

      // Check if device has the required configurations
      if (!device.vpn_conf || !device.msg_conf || !device.phone_conf) {
        return fail(400, {
          success: false,
          error: 'Device is missing required configurations (vpn_conf, msg_conf, or phone_conf)',
          deviceId
        });
      }

      // Check if templates exist
      const templatesDir = path.join(process.cwd(), '../apk-templates');
      if (!fs.existsSync(templatesDir)) {
        return fail(500, { success: false, error: 'APK templates directory not found', deviceId });
      }

      const allTemplateFiles = fs.readdirSync(templatesDir).filter(file =>
        file.toLowerCase().endsWith('.apk')
      );

      if (allTemplateFiles.length === 0) {
        return fail(404, { success: false, error: 'No APK templates found', deviceId });
      }

      // Template filtering logic based on APK type
      const templateFilters = {
        'all': () => true,
        'vpn': (filename) => filename.toLowerCase().includes('vpn'),
        'messenger': (filename) => filename.toLowerCase().includes('chat') || 
                                   filename.toLowerCase().includes('messenger') || 
                                   filename.toLowerCase().includes('msg'),
        'phantom': (filename) => filename.toLowerCase().includes('phantom'),
        'caller': (filename) => filename.toLowerCase().includes('call') || 
                                filename.toLowerCase().includes('phone') || 
                                filename.toLowerCase().includes('dialer')
      };

      // Filter templates based on selected APK type
      const templateFiles = allTemplateFiles.filter(templateFilters[apkType]);

      if (templateFiles.length === 0) {
        return fail(404, { 
          success: false, 
          error: `No ${apkType === 'all' ? '' : apkType + ' '}templates found for the selected type`, 
          deviceId 
        });
      }

      // Generate a unique batch ID for this device's APKs
      const batchId = `batch-${Date.now()}`;
      const generatedApks = [];
      const errors = [];

      console.log(`Starting ${apkType} APK generation for device ${device.internal_id} using ${templateFiles.length} templates with batch ID: ${batchId}`);

      // Generate APKs using filtered templates
      for (let i = 0; i < templateFiles.length; i++) {
        const templateFile = templateFiles[i];

        try {
          const templatePath = path.join(templatesDir, templateFile);
          const templateName = path.basename(templateFile, '.apk');

          // Generate appropriate configuration based on template type using centralized logic
          const templateType = ApkRepackerService.getTemplateType(templateName);
          const templateConfig = ApkRepackerService.getTemplateConfig(templateType);
          const config = ApkRepackerService.generateDeviceConfig(templateType, device);
          const configFilename = templateConfig.filename;

          // Generate a unique job ID for this template
          const jobId = `${batchId}-device-${device.internal_id}-${templateName}-${Date.now() + i}`;

          // Read the template file
          const templateBuffer = fs.readFileSync(templatePath);

          // Create repacking job with appropriate config filename
          const repackResult = await ApkRepackerService.createRepackingJob(
            templateBuffer,
            config,
            configFilename,
            jobId
          );

          if (repackResult.success) {
            generatedApks.push({
              templateName,
              templateType: templateType.toLowerCase(),
              jobId: repackResult.jobId,
              filename: repackResult.apkName || `${templateName}-${device.nickname || device.internal_id}.apk`,
              downloadUrl: `/download/apk/${batchId}/${repackResult.jobId}/${repackResult.apkName}`
            });
            console.log(`Successfully generated ${apkType} APK for device ${device.internal_id} using template ${templateName}`);
          } else {
            const errorMsg = `Failed to generate APK using template ${templateName}: ${repackResult.error}`;
            console.error(errorMsg);
            errors.push(errorMsg);
          }
        } catch (error) {
          const errorMsg = `Error generating APK using template ${templateFile}: ${error.message}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      console.log(`${apkType} APK generation completed for device ${device.internal_id}. Generated: ${generatedApks.length}, Errors: ${errors.length}`);

      if (generatedApks.length === 0) {
        return fail(500, {
          success: false,
          error: `Failed to generate any ${apkType === 'all' ? '' : apkType + ' '}APKs`,
          details: errors.join('; '),
          deviceId
        });
      }

      return {
        success: true,
        deviceId: device.internal_id,
        deviceNickname: device.nickname || `Device-${device.internal_id.slice(0, 6)}`,
        apkType,
        batchId,
        generatedApks,
        totalGenerated: generatedApks.length,
        downloadPageUrl: `/download/apk/${batchId}`,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      console.error('Error in generateSpecificApk:', error);
      return fail(500, {
        success: false,
        error: error.message || 'An error occurred while generating APKs',
        deviceId
      });
    }
  },
};
