import { dev } from '$app/environment';
import { getSession, verifySession } from '$lib/server/session';
import { verifyCsrfToken } from '$lib/server/csrf';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
  // Clean up completed jobs (older than 1 hour) when the page loads
  try {
    const completedJobsRemoved = ApkRepackerService.cleanupCompletedJobs();
    if (completedJobsRemoved > 0) {
      console.log(
        `Cleaned up ${completedJobsRemoved} completed APK repacking jobs on page load`
      );
    }

    // Also clean up very old files (older than 24 hours)
    const oldFilesRemoved = ApkRepackerService.cleanupTempFiles();
    if (oldFilesRemoved > 0) {
      console.log(
        `Cleaned up ${oldFilesRemoved} old APK repacking directories on page load`
      );
    }
  } catch (error) {
    console.error('Error cleaning up temporary files:', error);
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  // Repack APK action
  repackApk: async ({ request, cookies }) => {
    // Clean up completed jobs before starting a new one
    try {
      const completedJobsRemoved = ApkRepackerService.cleanupCompletedJobs();
      if (completedJobsRemoved > 0) {
        console.log(
          `Cleaned up ${completedJobsRemoved} completed APK repacking jobs before starting new job`
        );
      }
    } catch (error) {
      console.error('Error cleaning up completed jobs:', error);
    }

    const data = await request.formData();
    const csrfToken = data.get('csrfToken');
    const config = data.get('config');
    const apkFile = data.get('apkFile');
    const assetFilename = data.get('assetFilename') || 'vpn.conf';
    const componentId = data.get('componentId'); // Get the component ID for creating a unique job ID

    // Verify the session
    const signedSession = cookies.get('admin_session');
    /** @type {string|null} */
    const sessionId = verifySession(signedSession);
    const session = sessionId ? getSession(sessionId) : null;

    // If not authenticated or CSRF token is invalid, return error
    if (!dev && !session) {
      return { success: false, error: 'Authentication required' };
    }

    // Verify CSRF token
    if (!dev && !sessionId) {
      return { success: false, error: 'Invalid session' };
    }

    if (csrfToken) {
      const tokenStr =
        typeof csrfToken === 'string' ? csrfToken : String(csrfToken);
      if (!dev && !verifyCsrfToken(tokenStr, sessionId)) {
        return { success: false, error: 'Invalid request token' };
      }
    } else {
      return { success: false, error: 'CSRF token required' };
    }

    // Validate inputs
    if (!apkFile) {
      return { success: false, error: 'APK file is required' };
    }

    // Log file information for debugging
    try {
      if (apkFile instanceof File) {
        console.log('File details:', {
          name: apkFile.name,
          type: apkFile.type,
          size: apkFile.size,
        });
      } else if (apkFile.constructor && apkFile.constructor.name === 'File') {
        // Handle non-instanceof but File-like object
        // Use type assertion to avoid TypeScript errors
        const fileLike = apkFile;
        console.log('File-like details:', {
          name: fileLike.name,
          type: fileLike.type,
          size: fileLike.size,
        });
      }
    } catch (err) {
      console.error('Error logging file info:', err);
    }

    // Ensure we have a File object
    if (
      !(apkFile instanceof File) &&
      !(apkFile.constructor && apkFile.constructor.name === 'File')
    ) {
      return {
        success: false,
        error: 'Invalid file upload',
        details: `Expected File object, got ${typeof apkFile}`,
      };
    }

    // From this point, we know apkFile is a File object
    const file = /** @type {File} */ (apkFile);

    // Check if the file has content
    if (file.size === 0) {
      return { success: false, error: 'The uploaded APK file is empty' };
    }

    // Check if the file has a valid APK extension
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.apk')) {
      return {
        success: false,
        error: 'The uploaded file must be an APK file (.apk extension)',
      };
    }

    // Check if the file size is reasonable (at least 1MB for a valid APK)
    if (file.size < 1024 * 1024) {
      return {
        success: false,
        error: 'The uploaded APK file is too small',
        details: `File size: ${(file.size / 1024).toFixed(2)} KB. A valid APK file should be at least 1MB.`,
      };
    }

    if (!config) {
      return { success: false, error: 'Configuration is required' };
    }

    // Use the APK Repacker Service to create a new repacking job
    const configStr = typeof config === 'string' ? config : String(config);
    const assetFilenameStr =
      typeof assetFilename === 'string' ? assetFilename : String(assetFilename);

    // Create a custom job ID using the component ID if available
    let customJobId = undefined; // Use undefined instead of null to match the expected type
    if (componentId) {
      // Use the component ID as part of the job ID to ensure uniqueness
      customJobId = `${Date.now()}-${componentId}`;
      console.log(`Creating repacking job with custom ID: ${customJobId}`);
    }

    const result = await ApkRepackerService.createRepackingJob(
      file,
      configStr,
      assetFilenameStr,
      customJobId
    );

    // Return the result directly
    return result;
  },
};
