import { v4 as uuidv4 } from 'uuid';
import { generateTeamWallets } from '$lib/server/walletService.js';
import { deleteTeamWithAssociatedData, createTeam } from '$lib/server/teamService.js';
import { getTeamRepository, getDatabaseFactory } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';

export async function load() {
  try {
    const teamRepository = getTeamRepository();

    // Get all teams for display first (simpler operation)
    const teamsResult = await teamRepository.findMany();

    if (!teamsResult.success) {
      console.error('Failed to get teams:', teamsResult.error);
      return {
        teams: [],
        error: teamsResult.error?.message || 'Failed to load teams',
        totalTeams: 0,
        totalBalance: 0,
      };
    }

    // Get team statistics using the repository
    const statisticsResult = await teamRepository.getStatistics();

    if (!statisticsResult.success) {
      console.error('Failed to get team statistics:', statisticsResult.error);
      // Don't fail completely, just use basic stats from teams data
      const teams = teamsResult.data || [];
      const totalBalance = teams.reduce((sum, team) => sum + (team.balance || 0), 0);

      return {
        teams: teams.map(team => team.toSummary ? team.toSummary() : team),
        error: null,
        totalTeams: teams.length,
        totalBalance: totalBalance,
      };
    }

    const statistics = statisticsResult.data;
    const teams = teamsResult.data || [];

    return {
      teams: teams.map(team => team.toSummary ? team.toSummary() : team),
      error: null,
      totalTeams: statistics.totalTeams,
      totalBalance: statistics.totalBalance,
    };
  } catch (e) {
    console.error('Error in teams page load:', e);
    const errorMessage = e instanceof Error ? e.message : String(e);
    return {
      teams: [],
      error: errorMessage,
      totalTeams: 0,
      totalBalance: 0
    };
  }
}

export const actions = {
  create: async ({ request }) => {
    const formData = await request.formData();
    const id = formData.get('id');
    const balanceEntry = formData.get('balance');
    const balance =
      typeof balanceEntry === 'string' ? parseFloat(balanceEntry) : NaN;
    const chargeAmountEntry = formData.get('charge_amount');
    const charge_amount =
      typeof chargeAmountEntry === 'string' ? parseFloat(chargeAmountEntry) : NaN;
    const next_charge_at = formData.get('next_charge_at');
    const owner_id = formData.get('owner_id');

    // Validate required fields
    if (!id || typeof balance !== 'number' || isNaN(balance) || typeof charge_amount !== 'number' || isNaN(charge_amount)) {
      return {
        success: false,
        error: 'Missing or invalid required fields: id, balance, charge_amount',
      };
    }

    // Prepare team data
    const teamData = {
      id: String(id),
      charge_amount: charge_amount,
      balance: balance,
      owner_id: owner_id ? String(owner_id) : null,
    };

    if (next_charge_at && typeof next_charge_at === 'string') {
      teamData.next_charge_at = new Date(next_charge_at);
    }

    // Use the team service to create the team
    const createResult = await createTeam(teamData);

    if (!createResult.success) {
      return {
        success: false,
        error: createResult.error || 'Failed to create team'
      };
    }

    const createdTeam = createResult.data;

    // Generate wallets for the new team
    try {
      await generateTeamWallets(createdTeam.internal_id, createdTeam.id);
    } catch (walletError) {
      console.error(
        `Failed to generate wallets for team ${createdTeam.id}:`,
        walletError
      );
      // Don't fail the team creation if wallet generation fails
      // The team is created successfully, wallets can be generated later
    }

    return {
      success: true,
      data: createdTeam.toSummary ? createdTeam.toSummary() : createdTeam
    };
  },
  delete: async ({ request }) => {
    const formData = await request.formData();
    const id = formData.get('id');

    if (!id || typeof id !== 'string') {
      return { success: false, error: 'Missing or invalid team ID' };
    }

    return await deleteTeamWithAssociatedData(id);
  },
};
