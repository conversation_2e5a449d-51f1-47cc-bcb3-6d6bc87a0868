import { dev } from '$app/environment';
import { getSession, verifySession } from '$lib/server/session';
import { verifyCsrfToken } from '$lib/server/csrf';
import { env } from '$env/dynamic/private';

/** @type {import('./$types').PageServerLoad} */
export async function load({ fetch, parent }) {
  // Fetch clients data from API
  try {
    // Fetch clients data with authorization header
    const clientsResponse = await fetch('/api/clients', {
      headers: {
        Authorization: `Bearer ${env.API_KEY}`,
      },
    });
    const clientsData = await clientsResponse.json();

    return {
      clients: clientsData.clients || [],
      csrfToken,
    };
  } catch (error) {
    return {
      clients: [],
      error: 'Failed to load data',
    };
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  // Toggle client status
  toggleClient: async ({ request, cookies, fetch }) => {
    const data = await request.formData();
    const clientId = data.get('clientId');
    const enabled = data.get('enabled') === 'true';
    const csrfToken = data.get('csrfToken');

    // Verify the session
    const signedSession = cookies.get('admin_session');
    /** @type {string|null} */
    const sessionId = verifySession(signedSession);
    const session = sessionId ? getSession(sessionId) : null;

    // If not authenticated or CSRF token is invalid, return error
    if (!dev && !session) {
      return { success: false, error: 'Authentication required' };
    }

    // Verify CSRF token
    if (!dev && !sessionId) {
      return { success: false, error: 'Invalid session' };
    }

    const tokenStr =
      typeof csrfToken === 'string' ? csrfToken : String(csrfToken);
    if (!dev && !verifyCsrfToken(tokenStr, sessionId)) {
      return { success: false, error: 'Invalid request token' };
    }

    try {
      const response = await fetch(`/api/client/${clientId}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${env.API_KEY}`,
        },
        body: JSON.stringify({
          enabled: !enabled,
        }),
      });

      const result = await response.json();

      if (result.success) {
        return {
          success: true,
          message: enabled
            ? `Client ${clientId} has been disabled`
            : `Client ${clientId} has been enabled`,
        };
      } else {
        return {
          success: false,
          error: result.message || 'Failed to toggle client status',
        };
      }
    } catch (error) {
      return { success: false, error: 'Failed to toggle client status' };
    }
  },

  // Trust node's state (resolve conflict)
  trustNodeState: async ({ request, cookies, fetch }) => {
    const data = await request.formData();
    const clientId = data.get('clientId');
    const csrfToken = data.get('csrfToken');

    // Verify the session
    const signedSession = cookies.get('admin_session');
    /** @type {string|null} */
    const sessionId = verifySession(signedSession);
    const session = sessionId ? getSession(sessionId) : null;

    // If not authenticated or CSRF token is invalid, return error
    if (!dev && !session) {
      return { success: false, error: 'Authentication required' };
    }

    // Verify CSRF token
    if (!dev && !sessionId) {
      return { success: false, error: 'Invalid session' };
    }

    const tokenStr =
      typeof csrfToken === 'string' ? csrfToken : String(csrfToken);
    if (!dev && !verifyCsrfToken(tokenStr, sessionId)) {
      return { success: false, error: 'Invalid request token' };
    }

    try {
      const response = await fetch(`/api/client/${clientId}/trust-node`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${env.API_KEY}`,
        },
      });

      const result = await response.json();

      if (result.success) {
        return { success: true, message: result.message };
      } else {
        return {
          success: false,
          error: result.message || 'Failed to trust node state',
        };
      }
    } catch (error) {
      return { success: false, error: 'Failed to trust node state' };
    }
  },
};
