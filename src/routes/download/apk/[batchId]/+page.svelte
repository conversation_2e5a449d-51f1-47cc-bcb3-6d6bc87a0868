<script>
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  export let data;

  let downloading = {};
  let downloadedCount = 0;
  let isWebView = false;
  let userAgent = '';
  let showCopyLinks = false;

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function formatDate(date) {
    if (!date) return null;
    
    try {
      const dateObj = new Date(date);
      // Check if the date is valid and not the Unix epoch (1970-01-01)
      if (isNaN(dateObj.getTime()) || dateObj.getTime() === 0) {
        return null;
      }
      
      return dateObj.toLocaleString();
    } catch (error) {
      console.warn('Error formatting date:', error, 'Input:', date);
      return null;
    }
  }

  // Detect if we're running in a WebView or mobile browser
  function detectWebView() {
    if (!browser) return false;

    userAgent = navigator.userAgent;

    // Check for common WebView indicators
    const webViewIndicators = [
      'wv', // Android WebView
      'WebView',
      'Version/4.0', // Android WebView often has this
      'Mobile Safari' // But not full Safari
    ];

    // Check if it's Android and has WebView indicators
    const isAndroid = /Android/i.test(userAgent);
    const hasWebViewIndicator = webViewIndicators.some(indicator =>
      userAgent.includes(indicator)
    );

    // Additional check: if it's Android but doesn't have Chrome in user agent
    const isAndroidWithoutChrome = isAndroid && !userAgent.includes('Chrome');

    return isAndroid && (hasWebViewIndicator || isAndroidWithoutChrome);
  }

  // Copy download link to clipboard
  async function copyDownloadLink(apkFile) {
    try {
      await navigator.clipboard.writeText(apkFile.downloadUrl);
      alert(`Download link copied to clipboard for ${apkFile.friendlyFilename || apkFile.filename}`);
    } catch (error) {
      console.error('Failed to copy link:', error);
      // Fallback: show the link in a prompt
      prompt('Copy this download link:', apkFile.downloadUrl);
    }
  }

  // WebView-compatible download method
  function downloadFileWebView(apkFile) {
    try {
      // Method 1: Try creating a hidden link and clicking it
      const link = document.createElement('a');
      link.href = apkFile.downloadUrl;
      link.download = apkFile.friendlyFilename || apkFile.filename;
      link.style.display = 'none';
      link.target = '_blank'; // Open in new tab/window for WebView

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Mark as downloaded after a short delay
      setTimeout(() => {
        downloadedCount++;
        downloading[apkFile.filename] = false;
        downloading = { ...downloading };
        console.log(`WebView download initiated for ${apkFile.filename}`);
      }, 1000);

    } catch (error) {
      console.error('Link method failed, trying iframe method:', error);

      try {
        // Method 2: Try creating a hidden iframe for download
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = apkFile.downloadUrl;
        document.body.appendChild(iframe);

        // Clean up iframe after a delay
        setTimeout(() => {
          if (document.body.contains(iframe)) {
            document.body.removeChild(iframe);
          }
        }, 5000);

        setTimeout(() => {
          downloadedCount++;
          downloading[apkFile.filename] = false;
          downloading = { ...downloading };
          console.log(`WebView download initiated via iframe for ${apkFile.filename}`);
        }, 1000);

      } catch (iframeError) {
        console.error('Iframe method also failed, falling back to direct navigation:', iframeError);
        // Final fallback: Direct navigation
        window.open(apkFile.downloadUrl, '_blank');

        setTimeout(() => {
          downloadedCount++;
          downloading[apkFile.filename] = false;
          downloading = { ...downloading };
          console.log(`WebView download initiated via window.open for ${apkFile.filename}`);
        }, 1000);
      }
    }
  }

  // Standard browser download method
  async function downloadFileBrowser(apkFile) {
    const response = await fetch(apkFile.downloadUrl);

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = apkFile.friendlyFilename || apkFile.filename;
    document.body.appendChild(a);
    a.click();

    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    downloadedCount++;
    console.log(`Successfully downloaded ${apkFile.filename}`);
  }

  async function downloadFile(apkFile) {
    if (downloading[apkFile.filename]) return;

    downloading[apkFile.filename] = true;
    downloading = { ...downloading }; // Trigger reactivity

    try {
      if (isWebView) {
        downloadFileWebView(apkFile);
      } else {
        try {
          await downloadFileBrowser(apkFile);
        } catch (error) {
          console.error(`Browser download failed, trying WebView method:`, error);
          // Fallback to direct navigation if blob download fails
          downloadFileWebView(apkFile);
        }
      }
    } catch (error) {
      console.error(`Error downloading ${apkFile.filename}:`, error);
      alert(`Error downloading ${apkFile.filename}: ${error.message}`);
      downloading[apkFile.filename] = false;
      downloading = { ...downloading };
    }
  }

  async function downloadAll() {
    if (data.apkFiles.length === 0) return;

    // For WebView, use longer delays and show a warning
    const delay = isWebView ? 2000 : 500;

    if (isWebView && data.apkFiles.length > 1) {
      const proceed = confirm(
        `WebView detected. Multiple downloads may not work as expected. ` +
        `Consider downloading files individually. Continue anyway?`
      );
      if (!proceed) return;
    }

    // Download files with appropriate delay between each
    for (let i = 0; i < data.apkFiles.length; i++) {
      const apkFile = data.apkFiles[i];

      if (!downloading[apkFile.filename]) {
        downloadFile(apkFile);

        // Add delay between downloads (longer for WebView)
        if (i < data.apkFiles.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }
  }

  onMount(() => {
    // Detect WebView environment
    isWebView = detectWebView();
    console.log(`Environment detected: ${isWebView ? 'WebView' : 'Browser'}`);
    console.log(`User Agent: ${userAgent}`);

    // Show copy links by default for WebView
    if (isWebView) {
      showCopyLinks = true;
    }

    // Auto-refresh the page every 30 seconds to check for new files
    const interval = setInterval(() => {
      if (browser) {
        window.location.reload();
      }
    }, 30000);

    return () => clearInterval(interval);
  });
</script>

<svelte:head>
  <title>Download APK Files - Batch {data.batchId}</title>
</svelte:head>

<div class="admin-container">
  <header class="admin-header">
    <div class="admin-title-container download-batch">
      <div class="batch-info download-batch">
        <span><strong>Batch ID:</strong> {data.batchId}</span>
        {#if data.apkFiles.length > 0}
          <span><strong>Device:</strong> {data.apkFiles[0].deviceId}</span>
        {/if}
        <span><strong>Generated:</strong> {formatDate(data.batchCreated)}</span>
        <span><strong>Total Files:</strong> {data.totalFiles}</span>
        {#if browser}
          <span class="download-method" title="Download method being used">
            <strong>Method:</strong> {isWebView ? 'WebView Compatible' : 'Standard Browser'}
          </span>
        {/if}
      </div>
    </div>
    {#if data.apkFiles.length > 0}
      <button
        class="button button-blue download-all-button"
        on:click={downloadAll}
        disabled={Object.values(downloading).some((d) => d)}
      >
        {#if Object.values(downloading).some((d) => d)}
          Downloading... ({downloadedCount}/{data.totalFiles})
        {:else}
          Download All APKs
        {/if}
      </button>

      {#if isWebView}
        <button
          class="button button-secondary"
          on:click={() => showCopyLinks = !showCopyLinks}
        >
          {showCopyLinks ? 'Hide' : 'Show'} Copy Links
        </button>
      {/if}
    {/if}
  </header>

  {#if isWebView}
    <div class="webview-notice">
      <h4>📱 Mobile App Detected</h4>
      <p>
        You're using this page within a mobile app. If downloads don't work automatically,
        try using the "Copy Link" buttons to copy download URLs and paste them in your browser.
      </p>
    </div>
  {/if}

  {#if data.apkFiles.length > 0}
    <div class="apk-list download-batch">
      {#each data.apkFiles as apkFile}
        <div class="apk-item download-batch">
          <div class="apk-info download-batch">
            <h3>{apkFile.serviceName || 'Unknown APK'}</h3>
            <div class="apk-details download-batch">
              <span class="file-size">{formatFileSize(apkFile.size)}</span>
              {#if formatDate(apkFile.created)}
                <span class="created-date small-text">{formatDate(apkFile.created)}</span>
              {/if}
              <span class="filename download-batch"
                >File: {apkFile.friendlyFilename || apkFile.filename}</span
              >
            </div>
          </div>
          <div class="apk-actions">
            <button
              class="button button-blue"
              on:click={() => downloadFile(apkFile)}
              disabled={downloading[apkFile.filename]}
            >
              {#if downloading[apkFile.filename]}
                <span class="spinner download-batch"></span>
                Downloading...
              {:else}
                Download
              {/if}
            </button>

            {#if showCopyLinks || isWebView}
              <button
                class="button button-secondary button-small"
                on:click={() => copyDownloadLink(apkFile)}
                title="Copy download link to clipboard"
              >
                📋 Copy Link
              </button>
            {/if}
          </div>
        </div>
      {/each}
    </div>
  {:else}
    <div class="no-files download-batch">
      <h2>No APK files found</h2>
      <p>The batch may still be processing or the files may have expired.</p>
      <button on:click={() => window.location.reload()}>Refresh Page</button>
    </div>
  {/if}
</div>
</div>
