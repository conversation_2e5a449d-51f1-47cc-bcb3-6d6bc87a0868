import { error } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';

/**
 * Handle GET requests to download APK files without authentication
 * This is a public endpoint that uses batch ID validation for security
 */
export async function GET({ params, url }) {
  const { batchId, jobId, filename } = params;

  // Validate batch ID format (should start with "batch-" followed by timestamp)
  if (!batchId || !batchId.startsWith('batch-')) {
    throw error(404, 'Invalid batch ID');
  }

  // Validate filename (prevent directory traversal)
  if (!filename || filename.includes('/') || filename.includes('\\') || !filename.toLowerCase().endsWith('.apk')) {
    throw error(400, 'Invalid filename');
  }

  // Validate job ID format
  if (!jobId || !jobId.includes(batchId)) {
    throw error(404, 'Invalid job ID');
  }

  try {
    // Construct the file path
    const filePath = path.join(ApkRepackerService.TMP_DIR, jobId, filename);

    // Log the download attempt for debugging
    console.log(`Public APK download attempt for batch: ${batchId}`);

    // Check if the file exists
    if (!fs.existsSync(filePath)) {
      console.error(`Public APK file not found for batch: ${batchId}`);
      throw error(404, 'APK file not found');
    }

    // Additional security check: ensure the file is within the expected directory structure
    const resolvedPath = path.resolve(filePath);
    const expectedBasePath = path.resolve(ApkRepackerService.TMP_DIR);
    
    if (!resolvedPath.startsWith(expectedBasePath)) {
      console.error(`Security violation: Path traversal attempt detected for batch: ${batchId}`);
      throw error(403, 'Access denied');
    }

    // Read the file
    const fileBuffer = fs.readFileSync(filePath);

    // Check if a friendly filename was provided via query parameter (for WebView compatibility)
    const queryDownloadName = url.searchParams.get('download');
    let downloadFilename = queryDownloadName || filename; // Use query param if available, otherwise default to original filename

    // If no query parameter was provided, extract service type from jobId and create user-friendly filename
    if (!queryDownloadName) {
      const serviceMatch = jobId.match(/-([^-]+)-\\d+$/);
      if (serviceMatch) {
        const serviceType = serviceMatch[1].toLowerCase();

        // Create user-friendly filename based on service type
        switch (serviceType) {
          case 'chat':
          case 'messenger':
            downloadFilename = 'chat.apk';
            break;
          case 'vpn':
            downloadFilename = 'vpn.apk';
            break;
          case 'phantom':
            downloadFilename = 'phantom.apk';
            break;
          case 'caller':
            downloadFilename = 'caller.apk';
            break;
          default:
            downloadFilename = `${serviceType}.apk`;
        }
      }
    }

    // Log successful download
    console.log(`Successfully serving public APK download: ${filename} as ${downloadFilename} (${fileBuffer.length} bytes)`);

    // Return the file as a response with appropriate headers
    return new Response(fileBuffer, {
      headers: {
        'Content-Type': 'application/vnd.android.package-archive',
        'Content-Disposition': `attachment; filename="${downloadFilename}"`,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Batch-ID': batchId, // Include batch ID in response headers for tracking
        // Additional headers for WebView compatibility
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
        'X-Content-Type-Options': 'nosniff',
        'Accept-Ranges': 'bytes'
      },
    });
  } catch (err) {
    console.error('Error serving public APK download:', err);
    
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    
    throw error(500, 'Error downloading APK file');
  }
}
